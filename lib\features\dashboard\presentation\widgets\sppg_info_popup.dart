import 'package:aplikasi_sppg/features/dashboard/domain/entities/sppg_location.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:intl/intl.dart';

class SPPGInfoPopup extends StatelessWidget {
  final SPPGLocation location;

  const SPPGInfoPopup({super.key, required this.location});

  @override
  Widget build(BuildContext context) {
    final theme = FluentTheme.of(context);
    return Card(
      padding: const EdgeInsets.all(16.0),
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 300),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(location.name, style: theme.typography.subtitle),
            const SizedBox(height: 8),
            _buildInfoRow(
              context,
              icon: FluentIcons.status_circle_ring,
              label: 'Status',
              value: _getStatusText(location.status),
              valueColor: _getStatusColor(context, location.status),
            ),
            const SizedBox(height: 4),
            _buildInfoRow(
              context,
              icon: FluentIcons.location,
              label: 'Alamat',
              value: location.address,
            ),
            const SizedBox(height: 4),
            _buildInfoRow(
              context,
              icon: FluentIcons.manufacturing,
              label: 'Produksi Hari Ini',
              value:
                  '${NumberFormat.decimalPattern().format(location.todayProduction)} porsi',
            ),
            const SizedBox(height: 4),
            _buildInfoRow(
              context,
              icon: FluentIcons.contact,
              label: 'Kontak',
              value: location.contactInfo?.name ?? 'N/A',
            ),
            const SizedBox(height: 12),
            Align(
              alignment: Alignment.bottomRight,
              child: Button(
                child: const Text('Lihat Detail'),
                onPressed: () {
                  // Navigate to SPPG detail page
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
  }) {
    final theme = FluentTheme.of(context);
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 16, color: theme.disabledColor),
        const SizedBox(width: 8),
        Expanded(
          child: RichText(
            text: TextSpan(
              style: theme.typography.body,
              children: [
                TextSpan(text: '$label: ', style: theme.typography.caption),
                TextSpan(
                  text: value,
                  style: theme.typography.body?.copyWith(
                    color: valueColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  String _getStatusText(SPPGStatus status) {
    switch (status) {
      case SPPGStatus.operational:
        return 'Operasional';
      case SPPGStatus.partiallyOperational:
        return 'Operasional Sebagian';
      case SPPGStatus.maintenance:
        return 'Dalam Perbaikan';
      case SPPGStatus.temporarilyClosed:
        return 'Tutup Sementara';
      case SPPGStatus.permanentlyClosed:
        return 'Tutup Permanen';
      default:
        return 'Tidak Diketahui';
    }
  }

  Color _getStatusColor(BuildContext context, SPPGStatus status) {
    switch (status) {
      case SPPGStatus.operational:
        return Colors.green.dark;
      case SPPGStatus.partiallyOperational:
        return Colors.orange.dark;
      case SPPGStatus.maintenance:
        return Colors.blue.dark;
      case SPPGStatus.temporarilyClosed:
        return Colors.red.dark;
      case SPPGStatus.permanentlyClosed:
        return Colors.black;
      default:
        return FluentTheme.of(context).disabledColor;
    }
  }
}
